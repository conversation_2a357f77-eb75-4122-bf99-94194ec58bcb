"""
POTIONS Payment URLs - post-payment refund operations only
"""
from django.urls import path
from payments.views import (
    CreateRefundView,
    GetRefundStatusView,
    RazorpayWebhookView
)

urlpatterns = [
    # Refund operations (post-payment)
    path('refund/create/', CreateRefundView.as_view(), name='create_refund'),
    path('refund/status/<str:refund_id>/', GetRefundStatusView.as_view(), name='get_refund_status'),
    
    # Razorpay webhook for post-payment events
    path('webhook/', RazorpayWebhookView.as_view(), name='razorpay_webhook'),
]
