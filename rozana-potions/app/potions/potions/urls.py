"""
URL configuration for potions project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from integrations.views.health import (
    HealthCheckAPIView
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),

    # Health check endpoint (top-level for easier access)
    path('health/', HealthCheckAPIView.as_view(), name='health_check'),

    path('api/potions/integrations/', include('integrations.urls')),
    path('api/potions/payments/', include('payments.urls')),
    path('scripts/', include('scripts.urls')),
]
