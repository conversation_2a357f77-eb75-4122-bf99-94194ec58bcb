from typing import Dict, Any, List, Optional
from django.db import connections

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('repository_oms_main')

class OMSDatabase:
    """ORM wrapper for OMS database operations"""
    def __init__(self):
        self.db_alias = 'oms'

    def get_connection(self):
        """Get Django database connection for OMS"""
        return connections[self.db_alias]

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a query on OMS database"""
        try:
            oms_db = self.get_connection()
            with oms_db.cursor() as cursor:
                cursor.execute(query, params or [])
                columns = [col[0] for col in cursor.description]
                return [
                    dict(zip(columns, row)) 
                    for row in cursor.fetchall()
                ]
        except Exception as e:
            logger.error(f"Database query error: {e}")
            raise

    def execute_update(self, query: str, params: Dict[str, Any]) -> int:
        """Execute an update query on OMS database"""
        try:
            oms_db = self.get_connection()
            with oms_db.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Database update error: {e}")
            raise

    def execute_insert(self, query: str, params: Dict[str, Any]) -> int:
        """Execute an insert query on OMS database"""
        try:
            oms_db = self.get_connection()
            with oms_db.cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Insert execution error: {e}")
            raise

    def fetch_one(self, query: str, params: tuple = None) -> Optional[Dict]:
        """Fetch a single row from OMS database"""
        try:
            oms_db = self.get_connection()
            with oms_db.cursor() as cursor:
                cursor.execute(query, params or [])
                result = cursor.fetchone()
                if result:
                    columns = [col[0] for col in cursor.description]
                    return dict(zip(columns, result))
                return None
        except Exception as e:
            logger.error(f"Database fetch error: {e}")
            raise