
# Types
from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_repository')

# Database wrapper
from core.repository.main import OMSDatabase


class RefundRepository:
    """Repository for refund operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def create_refund_record(
        self,
        payment_id: int,
        refund_id: str,
        refund_amount: Decimal,
        refund_status: int = 60  # RefundStatus.CREATED
    ) -> int:
        """Create a refund record in OMS database"""
        query = """
            INSERT INTO refund_details (
                payment_id, refund_id, refund_amount, refund_status,
                created_at, updated_at
            ) VALUES (
                %(payment_id)s, %(refund_id)s, %(refund_amount)s, %(refund_status)s,
                NOW(), NOW()
            ) RETURNING id
        """

        params = {
            'payment_id': payment_id,
            'refund_id': refund_id,
            'refund_amount': refund_amount,
            'refund_status': refund_status
        }

        return self.db.execute_insert(query, params)

    def update_refund_status(self, refund_id: str, new_status: int, refund_date: datetime = None) -> bool:
        """Update refund status by refund_id"""
        query = """
            UPDATE refund_details 
            SET refund_status = %(new_status)s, 
                refund_date = %(refund_date)s,
                updated_at = NOW()
            WHERE refund_id = %(refund_id)s
        """

        affected_rows = self.db.execute_update(query, {
            'refund_id': refund_id,
            'new_status': new_status,
            'refund_date': refund_date or datetime.now(timezone.utc)
        })

        return affected_rows > 0

    def get_refund_by_id(self, refund_id: str) -> Optional[Dict]:
        """Get refund record by refund_id"""
        query = """
            SELECT r.id, r.payment_id, r.refund_id, r.refund_amount, r.refund_status, 
                   r.refund_date, r.created_at, r.updated_at, p.payment_id, p.order_id 
            FROM refund_details r
            JOIN payment_details p ON r.payment_id = p.id
            WHERE r.refund_id = %s
        """
        return self.db.fetch_one(query, (refund_id,))

    def get_refunds_for_payment(self, payment_id: int) -> List[Dict]:
        """Get all refunds for a payment"""
        query = """
            SELECT id, payment_id, refund_id, refund_amount, refund_status, refund_date, created_at, updated_at
            FROM refund_details 
            WHERE payment_id = %s
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (payment_id,))

refund_repository = RefundRepository()

