from typing import Dict, Any, List, Optional

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('payment_repository')

# Database wrapper
from core.repository.main import OMSDatabase


class PaymentRepository:
    """Repository for payment operations on OMS database"""

    def __init__(self):
        """Initialize database connection"""
        self.db = OMSDatabase()

    def get_payment_details_by_payment_id(self, payment_id: str) -> Optional[Dict]:
        """Get payment record by external payment_id"""
        query = """
            SELECT * FROM payment_details 
            WHERE payment_id = %s
        """
        return self.db.fetch_one(query, (payment_id,))

    def get_payment_details_by_internal_order_id(self, internal_order_id: int) -> List[Dict]:
        """Get all payment records for an internal order"""
        query = """
            SELECT * FROM payment_details 
            WHERE order_id = %s
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (internal_order_id,))

    def get_payment_details_by_order_id(self, order_id: str) -> Optional[Dict]:
        """Get payment record by order_id"""
        query = """
            SELECT * FROM payment_details
            JOIN orders ON payment_details.order_id = orders.id
            WHERE orders.order_id = %s
        """
        return self.db.fetch_one(query, (order_id,))

    def get_payment_details_by_order_id_and_payment_mode(self, order_id: str, payment_mode: str) -> Optional[Dict]:
        """Get payment record for an order and payment mode"""
        query = """
            SELECT * FROM payment_details
            JOIN orders ON payment_details.order_id = orders.id
            WHERE orders.order_id = %s AND payment_details.payment_mode = %s
            ORDER BY payment_details.created_at DESC
        """
        return self.db.fetch_one(query, (order_id, payment_mode))

    def update_payment_details_status(self, payment_id: str, new_status: int) -> bool:
        """Update payment status"""
        query = """
            UPDATE payment_details 
            SET payment_status = %(new_status)s, updated_at = NOW()
            WHERE payment_id = %(payment_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'payment_id': payment_id,
            'new_status': new_status
        })
        return affected_rows > 0

payment_repository = PaymentRepository()