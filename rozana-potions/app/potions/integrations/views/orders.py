from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from integrations.tasks.wms_orders import (
    sync_order_to_liink_task,
    cancel_order_flow,
)
from integrations.services.liink_service import LiinkService
from core.models import Facility

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('orders_api')


class WMSOrderCreateAPIView(APIView):
    """
    API endpoint to trigger order creation in Liink
    Called by OMS when a new order is created
    """

    def post(self, request):
        """
        Trigger order sync to Liink as background job
        
        Expected payload:
        {
            "order_id": "POS41904046409_test1"
        }
        """
        try:
            order_id = request.data.get('order_id')
            
            if not order_id:
                return Response(
                    {
                        "success": False,
                        "error": "Missing required field: order_id",
                        "message": "order_id is required to sync order to Liink"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Fetch order data from OMS
            liink_service = LiinkService()
            order_data = liink_service.fetch_order_data_from_oms(order_id)

            if not order_data:
                return Response(
                    {
                        "success": False,
                        "error": "Order not found",
                        "message": f"Order {order_id} not found in OMS database"
                    },
                    status=status.HTTP_404_NOT_FOUND
                )

            # Validate facility WMS integration access before queuing task
            liink_service = LiinkService()
            facility_name = order_data.get("facility_name")
            if not liink_service._validate_facility_wms_access(facility_name):
                return Response(
                    {
                        "success": False,
                        "error": "WMS integration disabled",
                        "message": f"WMS order creation is disabled for facility: {facility_name}"
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            # Trigger Celery task using apply_async
            task = sync_order_to_liink_task.apply_async(
                args=[order_id],
                retry=True
            )
            
            logger.info(f"Triggered Liink sync task for order {order_id}, task_id: {task.id}")
            
            return Response(
                {
                    "success": True,
                    "order_id": order_id,
                    "task_id": task.id,
                    "message": "Order sync to Liink triggered successfully"
                },
                status=status.HTTP_202_ACCEPTED
            )
            
        except Exception as e:
            logger.error(f"Error triggering Liink sync for order: {str(e)}")
            return Response(
                {
                    "success": False,
                    "error": str(e),
                    "message": "Failed to trigger order sync to Liink"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WMSOrderCancelAPIView(APIView):
    """API endpoint to cancel an order in WMS"""

    def post(self, request):
        """Trigger order cancel in WMS as background job.

        Expected payload::

            {
                "order_reference": "8000209",
                "facility_id": "hr009_pla_ls1"
            }
        """
        try:
            order_reference = request.data.get('order_reference')
            warehouse = request.data.get('facility_id') or request.data.get('warehouse')

            if not order_reference or not warehouse:
                return Response(
                    {
                        "success": False,
                        "error": "Missing required fields",
                        "message": "order_reference and facility_id are required",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            task = cancel_order_flow.apply_async(
                args=[order_reference, warehouse],
                retry=True,
            )

            logger.info(
                f"Triggered WMS cancel task for order {order_reference}, task_id: {task.id}"
            )

            return Response(
                {
                    "success": True,
                    "order_reference": order_reference,
                    "task_id": task.id,
                    "message": "Order cancel triggered successfully",
                },
                status=status.HTTP_202_ACCEPTED,
            )

        except Exception as e:
            logger.exception("Error triggering WMS cancel for order: %s", str(e))
            return Response(
                {
                    "success": False,
                    "error": str(e),
                    "message": "Failed to trigger order cancel in WMS",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
